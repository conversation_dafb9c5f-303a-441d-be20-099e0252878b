import { HardhatUserConfig } from "hardhat/config";
import "@nomicfoundation/hardhat-toolbox";
import '@openzeppelin/hardhat-upgrades';
require("dotenv").config();

if (!process.env.PRIVATE_KEY) {
  throw new Error("Please set your PRIVATE_KEY in a .env file");
}

const config: HardhatUserConfig = {
  solidity: {
    compilers: [
      {
        version: `0.8.24`,
        settings: {
          optimizer: {
            enabled: true,
            runs: 200
          },
          evmVersion: `paris`, // downgrade to `paris` if you encounter 'invalid opcode' error
        }
      },
    ],
  },
  networks: {
    sepolia: {
      accounts: [process.env.PRIVATE_KEY],
      url: process.env.SEPOLIA_RPC_URL,
    },
    amoy: {
      accounts: [process.env.PRIVATE_KEY],
      url: process.env.MUMBAI_RPC_URL,
      chainId: 80002,
    },
    baseTestnet: {
      accounts: [process.env.PRIVATE_KEY],
      url: process.env.BASE_RPC_URL,
      chainId: 8453,
    }
  },
  etherscan: {
    apiKey: process.env.ETHERSCAN_API_KEY,
    customChains: [
      {
        network: "amoy",
        chainId: 80002,
        urls: {
          apiURL: "https://www.oklink.com/api/explorer/v1/contract/verify/async/api/polygonAmoy",
          browserURL: "https://www.okx.com/polygonAmoy"
        }
      }
    ],
  }
};

export default config;
