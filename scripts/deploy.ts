import { ethers, upgrades } from "hardhat";

async function main() {

  const [deployer] = await ethers.getSigners();
    
  console.log("Deploying contracts with the account:", deployer.address);

  // deploy SBT
  const SBT = await ethers.getContractFactory("SBT");
  const sbt = await SBT.deploy();

  await sbt.waitForDeployment();

  console.log(
    "SBT deployed to:",
    await sbt.getAddress()
  );

  // deploy SBTMinting
    const SBTMinting = await ethers.getContractFactory("SBTMinting");
    const sbtMinting = await upgrades.deployProxy(SBTMinting, [deployer.address]);

    await sbtMinting.waitForDeployment();

    console.log(
        "SBTMinting deployed to:",
        await sbtMinting.getAddress()
    );

    // set SBTMinting as minter
    await sbt.setMintingContract(sbtMinting.getAddress());

    console.log("SBTMinting set as minter");

    //set sbt 
    await sbtMinting.setSBT(sbt.getAddress());

    console.log("SBT set in SBTMinting");

}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
