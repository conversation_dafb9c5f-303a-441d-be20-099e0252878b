// import { ethers, upgrades } from "hardhat";
// import Web3 from "web3";
// async function main() {

//   const [deployer] = await ethers.getSigners();
    
//   console.log("Deploying contracts with the account:", deployer.address);

//   if (!process.env.SBT_MINTING_ADDRESS) {
//     throw new Error("SBT_MINTING_ADDRESS is not set");
//   }
  
//   const SBTMinting = await ethers.getContractAt("SBTMinting", process.env.SBT_MINTING_ADDRESS);

//   await SBTMinting.waitForDeployment();

//   console.log(
//     "SBTMinting:",
//     await SBTMinting.getAddress()
//   );


//     // bytes32
//     const requestId = ethers.encodeBytes32String("request11");

//     // create mint request, with args: buyRequest, signature
//     // struct MintRequest {
//     //     address buyer;
//     //     string  did;
//     //     uint256 price;
//     //     uint256 deadline;
//     //     bytes32 requestId;
//     // }

//     // current time
//   const did = "did:example:1235";

//     const mintRequest = {
//         buyer: "******************************************",
//         price: 100000,
//         // deadline:  Math.floor(Date.now() / 1000) + 300, // 5 minutes
//         deadline: 100000000000,
//         requestId: requestId,
//         did: did,
//     }

//     // check if minted
//     const minted = await SBTMinting.isMinted(mintRequest.requestId);
//     if (minted) {
//         console.log("Already minted");
//         return;
//     }

//     // sign buy request with process.env.SIGNER_PRIVATE_KEY
//     // first, create a wallet from process.env.SIGNER_PRIVATE_KEY
//     if (!process.env.SIGNER_PRIVATE_KEY) {
//         throw new Error("SIGNER_PRIVATE_KEY is not set");
//     }
//     const signer = new ethers.Wallet(process.env.SIGNER_PRIVATE_KEY);

//     // check if the signer is the address that is supposed to sign the request
//     if (signer.address.toLowerCase() !== process.env.SIGNER_ADDRESS?.toLowerCase()) {
//         throw new Error("SIGNER_ADDRESS does not match the signer private key");
//     }
//     else {
//         console.log("Signer address: ", signer.address);
//     }

//     // const abiCoder = ethers.AbiCoder.defaultAbiCoder();
    
//     // let encoded = abiCoder.encode(
//     //     ["address", "uint256", "uint256", "bytes32", "string"],
//     //     [mintRequest.buyer, mintRequest.price, mintRequest.deadline, mintRequest.requestId, mintRequest.did]
//     // );

//     // console.log("encoded: ", encoded);
//     // remove the prefix 0x now
//     // encoded = encoded.slice(2);


//     // add 0x0000000000000000000000000000000000000000000000000000000000000020 to the start of the encoded data
//     // encoded = "0x0000000000000000000000000000000000000000000000000000000000000020" + encoded;

//     // console.log("encoded: ", encoded);

//     // then hash the buy request
//     // const hash = ethers.keccak256(encoded);

//     // console.log("hash: ", hash);

//     const hash3 = ethers.solidityPackedKeccak256(
//         ["address", "uint256", "uint256", "bytes32", "string"],
//         [mintRequest.buyer, mintRequest.price, mintRequest.deadline, mintRequest.requestId, mintRequest.did]
//     );

//     console.log("hash3: ", hash3);


//     // check if the hash is correct
//     const onchainHash = await SBTMinting.getABIEncoded(mintRequest);
//     // console.log("onchainHash: ", ethers.keccak256(onchainHash));

//     // compare the hash with the onchain hash
//     // if (encoded !== onchainHash) {
//     //     console.log("hash mismatch");
//     // }
    
//     // decode onchain hash
//     // const decoded = abiCoder.decode(
//     //     ["address", "uint256", "uint256", "bytes32", "string"],
//     //     encoded,
//     //     true
//     // );

//     // console.log("decoded: ", decoded);


//     console.log("onchainHash: ", onchainHash);

//     // console.log("hash with no keccak: ", encoded);
//     // if (hash !== onchainHash) {
//         // console.log("hash: ", hash);
//         // console.log("onchainHash: ",onchainHash);
//     //     throw new Error("Hash mismatch");
//     // }    

//     const web3Instance = new Web3(process.env.MUMBAI_RPC_URL);

//     // init web3 account from private key
//     const web3Account = web3Instance.eth.accounts.privateKeyToAccount(signer.privateKey);
//     console.log("Web3 account address: ", web3Account.address);


//     // // hash using web3 abi encode
//     // let hash2 = web3Instance.eth.abi.encodeParameters(
//     //     ["address", "uint256", "uint256", "bytes32", "string"],
//     //     [mintRequest.buyer, mintRequest.price, mintRequest.deadline, mintRequest.requestId, mintRequest.did]
//     // );

//     // // keccak256 the hash
//     // hash2 = web3Instance.utils.keccak256(hash2);

//     const hash2 = web3Instance.utils.soliditySha3(
//         {t: 'address', v: mintRequest.buyer},
//         {t: 'uint256', v: mintRequest.price},
//         {t: 'uint256', v: mintRequest.deadline},
//         {t: 'bytes32', v: mintRequest.requestId},
//         {t: 'string', v: mintRequest.did}
//     );

//     console.log("hash2: ", hash2);
//     // sign the hash
//     const signature = await web3Account.sign(hash2);
//     // then sign the hash
//     // const signature = await signer.signMessage(hash2);

//     // check if the signature is correct
//     const recovered = ethers.verifyMessage(hash2, signature);
//     console.log("recovered: ", recovered);

//     // if (recovered.toLowerCase() !== signer.address.toLowerCase()) {
//     //     throw new Error("Signature mismatch");
//     // }

//     const recoveredOnchain = await SBTMinting.getSignatureSigner(mintRequest, signature.signature);
//     console.log("recoveredOnchain: ", recoveredOnchain);

//     // if (recoveredOnchain.toLowerCase() !== signer.address.toLowerCase()) {
//     //     throw new Error("Signature mismatch");
//     // }

//     // now call the contract   
//     await SBTMinting.mintSBT(mintRequest, signature.signature, { value: mintRequest.price });

//     console.log("Buy request completed");

// }

// // We recommend this pattern to be able to use async/await everywhere
// // and properly handle errors.
// main().catch((error) => {
//   console.error(error);
//   process.exitCode = 1;
// });
