import { ethers, upgrades } from "hardhat";

async function main() {

  const [deployer] = await ethers.getSigners();
    
  console.log("Deploying contracts with the account:", deployer.address);

  if (!process.env.SBT_MINTING_ADDRESS) {
    throw new Error("SBT_MINTING_ADDRESS is not set");
  }

  const SBTMinting = await ethers.getContractFactory("SBTMinting");
  const sBTMinting = await upgrades.upgradeProxy(process.env.SBT_MINTING_ADDRESS, SBTMinting);

  await sBTMinting.waitForDeployment();

  console.log(
    "SBTMinting deployed to:",
    await sBTMinting.getAddress()
  );
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});