import { ethers, upgrades } from "hardhat";
import Web3 from "web3";
async function main() {

  const [deployer] = await ethers.getSigners();
    
  console.log("Deploying contracts with the account:", deployer.address);

  if (!process.env.SBT_MINTING_ADDRESS) {
    throw new Error("SBT_MINTING_ADDRESS is not set");
  }
  
  const SBTMinting = await ethers.getContractAt("SBTMinting", process.env.SBT_MINTING_ADDRESS);

  await SBTMinting.waitForDeployment();

  console.log(
    "SBTMinting:",
    await SBTMinting.getAddress()
  );

    // create mint request, with args: buyRequest, signature
    // struct MintRequest {
    //     address buyer;
    //     uint256 price;
    //     uint256 deadline;
    //     bytes32 requestId;
    //     string  did;
    // }

    // bytes32
    const requestId = ethers.encodeBytes32String("reque54st151");
    const did = "did:example:***********";
    const mintRequest = {
        buyer: "******************************************",
        price: 100000,
        deadline:  Math.floor(Date.now() / 1000) + 300, // 5 minutes
        requestId: requestId,
        did: did,
    }

    // check if minted
    const minted = await SBTMinting.isMinted(mintRequest.requestId);
    if (minted) {
        console.log("Already minted");
        return;
    }

    // sign buy request with process.env.SIGNER_PRIVATE_KEY
    // first, create a wallet from process.env.SIGNER_PRIVATE_KEY
    if (!process.env.SIGNER_PRIVATE_KEY) {
        throw new Error("SIGNER_PRIVATE_KEY is not set");
    }

    const web3Instance = new Web3(process.env.MUMBAI_RPC_URL);

    // init web3 account from private key
    const web3Account = web3Instance.eth.accounts.privateKeyToAccount(process.env.SIGNER_PRIVATE_KEY);
    console.log("Web3 account address: ", web3Account.address);

    const hash = web3Instance.utils.soliditySha3(
        {t: 'address', v: mintRequest.buyer},
        {t: 'uint256', v: mintRequest.price},
        {t: 'uint256', v: mintRequest.deadline},
        {t: 'bytes32', v: mintRequest.requestId},
        {t: 'string', v: mintRequest.did}
    );
    if (!hash) {
        throw new Error("Hash is empty");
    }
    
    console.log("hash: ", hash);
    // sign the hash
    const signature = web3Account.sign(hash);

    // now call the contract   
    try {
        const tx = await SBTMinting.mintSBT(mintRequest, signature.signature, { value: mintRequest.price });

    }
    catch (error: any) {
        if (error.data) {
            const decodedError = SBTMinting.interface.parseError(error?.data);
            console.log(`Transaction failed: ${decodedError?.name} and data: ${decodedError?.args}`);
        }
        else {
            console.log(`Transaction failed: ${error?.message}`);
        }
    }
    console.log("Buy request completed");

}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
