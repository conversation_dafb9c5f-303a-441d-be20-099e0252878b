// import { time } from "@nomicfoundation/hardhat-toolbox/network-helpers";
// import { ethers, upgrades } from "hardhat";

// async function main() {

//   const [deployer] = await ethers.getSigners();
    
//   console.log("Deploying contracts with the account:", deployer.address);

//   if (!process.env.SBT_MINTING_ADDRESS) {
//     throw new Error("SBT_MINTING_ADDRESS is not set");
//   }
  
//   const SBTMinting = await ethers.getContractAt("SBTMinting", process.env.SBT_MINTING_ADDRESS);

//   await SBTMinting.waitForDeployment();

//   console.log(
//     "SBTMinting:",
//     await SBTMinting.getAddress()
//   );


//     // bytes32
//     const requestId = ethers.encodeBytes32String("request1");

//     // create mint request, with args: buyRequest, signature
//     // struct MintRequest {
//     //     address buyer;
//     //     string  did;
//     //     uint256 price;
//     //     uint256 deadline;
//     //     bytes32 requestId;
//     // }

//     // current time


//     const mintRequest = {
//         buyer: deployer.address,
//         did: "did:example:123",
//         price: 100000n,
//         // deadline:  Math.floor(Date.now() / 1000) + 300, // 5 minutes
//         deadline: *********,
//         requestId: requestId,
//     }

//     // check if minted
//     const minted = await SBTMinting.isMinted(mintRequest.did);
//     if (minted) {
//         console.log("Already minted");
//         return;
//     }

//     // sign buy request with process.env.SIGNER_PRIVATE_KEY
//     // first, create a wallet from process.env.SIGNER_PRIVATE_KEY
//     if (!process.env.SIGNER_PRIVATE_KEY) {
//         throw new Error("SIGNER_PRIVATE_KEY is not set");
//     }
//     const signer = new ethers.Wallet(process.env.SIGNER_PRIVATE_KEY);

//     // check if the signer is the address that is supposed to sign the request
//     if (signer.address.toLowerCase() !== process.env.SIGNER_ADDRESS?.toLowerCase()) {
//         throw new Error("SIGNER_ADDRESS does not match the signer private key");
//     }
//     else {
//         console.log("Signer address: ", signer.address);
//     }

    
//     // then hash the buy request
//     // const hash = ethers.solidityPackedKeccak256(
//     //     ["address", "uint256", "uint256", "bytes32", "string"],
//     //     [mintRequest.buyer, mintRequest.price, mintRequest.deadline, mintRequest.requestId, mintRequest.did]
//     // );

//     // hash only request id
//     console.log("requestId: ", mintRequest.requestId);
//     // abi encode the requestId
//     const hash = ethers.solidityPackedKeccak256(["bytes32"], [mintRequest.requestId]);
//     console.log("hash: ", hash);

//     console.log("hash length: ", ethers.toUtf8Bytes(String(hash.length)));

//     const message = `\x19Ethereum Signed Message:\n${hash.length}${hash}`;
//     const newHash = ethers.keccak256(ethers.toUtf8Bytes(message));
//     console.log("newHash: ", newHash);

//     console.log("newHash length: ", newHash.length);

//     // ethers.keccak256(concat([
//     //     toUtf8Bytes(MessagePrefix),
//     //     toUtf8Bytes(String(message.length)),
//     //     message
//     // ]))
//     // check if the hash is correct
//     const onchainHash = await SBTMinting.getHash(mintRequest);

//     // console.log("onchainHash length: ", onchainHash.length);
//     // if (hash !== onchainHash) {
//     //     console.log("hash: ", hash);
//     //     console.log("onchainHash: ", onchainHash);
//     //     console.log("newHash: ", newHash);
//     //     throw new Error("Hash mismatch");
//     // }    

//     // then sign the hash
//     const signature = await signer.signMessage(hash);
//     console.log("signature: ", signature);

//     // check if the signature is correct
//     const recovered = ethers.verifyMessage(hash, signature);
//     console.log("recovered: ", recovered);

//     if (recovered.toLowerCase() !== signer.address.toLowerCase()) {
//         throw new Error("Signature mismatch");
//     }

//     // now call the contract   
//     await SBTMinting.mintSBT(mintRequest, signature, { value: mintRequest.price });


//     console.log("Buy request completed");

// }

// // We recommend this pattern to be able to use async/await everywhere
// // and properly handle errors.
// main().catch((error) => {
//   console.error(error);
//   process.exitCode = 1;
// });
