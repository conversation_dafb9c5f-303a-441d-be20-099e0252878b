import { ethers } from "hardhat";

async function main() {

  const [deployer] = await ethers.getSigners();
    
  console.log("Deploying contracts with the account:", deployer.address);

  if (!process.env.SBT_MINTING_ADDRESS) {
    throw new Error("SBT_MINTING_ADDRESS is not set");
  }
  
  const SBTMinting = await ethers.getContractAt("SBTMinting", process.env.SBT_MINTING_ADDRESS);

  await SBTMinting.waitForDeployment();

  console.log(
    "SBTMinting:",
    await SBTMinting.getAddress()
  );

    // set signer
    if (process.env.SIGNER_ADDRESS) {
        await SBTMinting.setSigner(process.env.SIGNER_ADDRESS);
        console.log("Signer address set to: ", process.env.SIGNER_ADDRESS);
    }
}

// We recommend this pattern to be able to use async/await everywhere
// and properly handle errors.
main().catch((error) => {
  console.error(error);
  process.exitCode = 1;
});
