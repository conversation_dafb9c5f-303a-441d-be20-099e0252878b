{"manifestVersion": "3.2", "proxies": [{"address": "0xC403150b93733D969C947052535b1D75b31F7442", "txHash": "0x591a923827170291af6076434b0c2797cf1403364c416e3b50f7d97224bcd7d7", "kind": "uups"}], "impls": {"86ec8f8320e0b0f2d14221ac615a6ce3e7a75ca0f747efe1f4eb785cddf922e1": {"address": "0x718eE2939171Fae4749C545F35350365A1233921", "txHash": "0x8befb783552f44e4365563976d533d7af4b7cd650c5a9096aa02ac38124b1973", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:17"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:19"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}