{"manifestVersion": "3.2", "proxies": [{"address": "0x0dD22180a5DfC514bB710beD7526Ef55B77D0b07", "txHash": "0xf1fea22a183d71b6dc718a2bb498d5d313d4e752cc814a9c860eb4e0b3b28fb5", "kind": "uups"}, {"address": "0x0e939197827F154f5114D376549360F95A430308", "txHash": "0x3a5629d6f6f7e178e24a5754f461540e2f88d3ee3df3f0e5af584e6d3477b25e", "kind": "uups"}, {"address": "0x6C4159BBbCdD66E471900dCdb830A41A408c1ea6", "txHash": "0x6e53129a509d5665cab28223a934dae46e46ba35b3066e8c5d4e6e18aadd5a28", "kind": "uups"}, {"address": "0xcfeCC9348aF66B733c5475c3f0a1854bAcb0694D", "txHash": "0x380a608f16a3260435c9c6569113e8910da65e4e0d6ece9fe469a77349e6e6ea", "kind": "uups"}, {"address": "0x5CAE89A7E8E2a045749410Dca608D4e1199D3D44", "txHash": "0x7ed4788ab3c4cbb06a0ffbb74e529d0b9da7d813b269b40f8a0c779e424670d2", "kind": "uups"}, {"address": "0x177d29a4dbB4bfAF4ac6857778c31099a7DC27bc", "txHash": "0xd6a055d65ee2b7237015951d514d60f0d86e854192a6cf844c07869ad8387e92", "kind": "uups"}, {"address": "0xe881F53E84ec2e5f886893C885C977cab70D5B39", "txHash": "0x13f7817833c6fdc8d31c140ecf66fe6ffc6edeb37a516426f7d29aec71d3ac59", "kind": "uups"}, {"address": "0x5465bE8863c4b8D8C073e7eda363E269a6141387", "txHash": "0x57d9e9c1340a8990705e38d5e27f9efd77f3d7a99b2362db70296965be218731", "kind": "uups"}, {"address": "0xbDEe3684A161233A9Da743bA9B09974c3DE99063", "txHash": "0x2259250858cb345f9c74fcf08d141088ae8d7aaaa8ba64221d8f23166ce0b54e", "kind": "uups"}, {"address": "0xafd84Eb255e34115Eaa7a19184D6DD91E8527853", "txHash": "0xf601cba16313de5940963a9dabb7b373afb41ed878c8c3abbfd120ff6773f10f", "kind": "uups"}, {"address": "0xdF404CF326E2DEb162Da9a60E957952FACe04bE7", "txHash": "0xf5a8ede857d7c27b3acf7d0ae4705fdbe133ebe0bb015f9e25d5c172600bde10", "kind": "uups"}, {"address": "0xF5b6A81108f67A0b0f06Acc30DAC71FCC3879A28", "txHash": "0x94253f93c60ceda5f536c33d8de18603390e826bd3cd4361308b6f40bb89e737", "kind": "uups"}, {"address": "0x4aBC9Ed96023b88d192Bce98A44d40dCd1ad3320", "txHash": "0x0cf54dd89f9e75d9f57b99529785719da911f882f9d79aa3d27a1121d11dcccf", "kind": "uups"}, {"address": "0x110Ac9B6E74019343119bF777Dd1279716699fa7", "txHash": "0x91ee4a735fadcf742f4724e82e40b42c550bc1387288d7bbf07acd75691c1562", "kind": "uups"}, {"address": "0x6143BF46ec85Ebe5A82A51fc307832889882c581", "txHash": "0x61f77d222364530db7ab9560c61714b2b973213b943cf4f9fda604cfb0b2f310", "kind": "uups"}, {"address": "0x6122DbCfC9dB8642B6E607E3EDF067603A20f543", "txHash": "0xd4862fbe8e6328343676b1f58e7e4632be00a9a7da639bdb8ff8cbd9dfc46df2", "kind": "uups"}, {"address": "0x01923a54fcf86faa1dfa3937E3d03fC96122efC1", "txHash": "0x9e4ecbb8dac0d3d7d5c8a3fbd3e59655891fe8e056447553481a90c024ef20bf", "kind": "uups"}, {"address": "0xCa3Bb661C1e27118029759abBFDDf2684d60dacb", "txHash": "0xc7b68febf5c11a4fd5bb479dad7d8ee70766ba8a17dc35eeeaae11490eac67bd", "kind": "uups"}, {"address": "0x4eb606FC9Eca852d4f428551D154A9f32C0Bc2E5", "txHash": "0x8b4a7120b42ae9fffc3122e2c5ed48dc8c29b65e38aa93cdbd598354905100bb", "kind": "uups"}, {"address": "0xb76215960C3C0794826751Fcd61572C531Bc9265", "txHash": "0x531da93547b161d5006deabfaa915d255bc0ea551aa8cc0d5132d6bc73c14d0c", "kind": "uups"}, {"address": "0x2A3C01A26E6fB49f570F94b5e670185963Dc5E2B", "txHash": "0xfb893de88c73e09585ad23c8b79064e5b2a51b7e9c342c61a9fcf683caabba7b", "kind": "uups"}, {"address": "0x0dF51e1149B26262Db708ab38EAE5768F9BE2944", "txHash": "0x784a55d20420f2b43d7e22ece45ebef3d9afea9542887b3a940561947ae3e39a", "kind": "uups"}], "impls": {"430f03689cdfa344ee34509f699e29f47226cf04df2797deac87bf629348d47a": {"address": "0x66296b2d4E08685219f3383dED6005D6e299029a", "txHash": "0x6f04a7c575103190391044c0f9f8d09d5b130b9af81e485273047df14379de67", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "5591ba08a990e89c04e15c2caf2f91c4a9011ae08995983ee0fbbb01f2e9b4d6": {"address": "0x0a0Db9DD7da72874308C22808BFaAAA5e050aC5f", "txHash": "0xcdbfb954572770f3d0194d8c89a9e2471e8ecabd046cb36b0eca643a7543d736", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "e6790a4d45b3d6edd83c6f26f7aae8561a7d89e0a620f2348d024abd5c23484f": {"address": "0xFa1c438a8Ac3D40433f3253Dba5C178eA0B652B7", "txHash": "0x3df92a5dc13a9f689858b37c7d7d7d57b72f6bf6d0327df606d329590e5dfa57", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "87a041dd87acf34f9bc0b4a5aca05411c8f8fea2f3d02251ef88201cd90dc2a9": {"address": "0x1c90476100Ec578Ce8F83f72cE25d3E4C658d566", "txHash": "0x2365cf2da9580bea4c7fd147036fd307b8b89c51cefadcdaff7bda91ccfbdc7a", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "a41bfa44ec9feb96ed3e28d0e9600e7df1a65ccd8c5114f1de9e1ae5917e55d6": {"address": "0x1F3B4bfB92B23861645C2cb50cCf02e74a471657", "txHash": "0xa92be54b4fa68d9750a2322b09512e950e95dc8631452578e78c37bb9368692d", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "fbfb91dd41692008a86fcf72764019da12e1c6065a22e4cd53bcacd7e045e6e4": {"address": "0x696c14374B7aAAd47d3348d5dC06D36349C28413", "txHash": "0xdd1eb0c4c3c6712779adfc1ea3100e6b0f8a574fe355f5110c9f92ad25a4744d", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:17"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:19"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "86ec8f8320e0b0f2d14221ac615a6ce3e7a75ca0f747efe1f4eb785cddf922e1": {"address": "0x89a3750b6896DCD2Fa29c38C92A390C6f8052488", "txHash": "0xface32c629075599264a4350b0002b6e4d1abec00bca1bcc1977f777296a6f80", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:17"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:19"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "bb52b2934095b44ed990356d167dc7c402797d81e9fa71bd8b1880f62508a769": {"address": "0x7460d5fA3bFD12dEE46f44afdd6355B721e58D91", "txHash": "0x014140185e62434f0aadf66e086ba59e4b38507a29af5c9bbae2b5a10558227a", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "bf7cf28d8c91ce387fe7492d5c0a8f75ce045506ebf15c58bf317aac5d407fdf": {"address": "0x5dc5653d253b73587084ED63f2E411Dc2274E24A", "txHash": "0xffdfb7ea108a7a3a184a782aa73514fb020c001fa407c6ff76438fb1b73af5ce", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "1fccd5218442aaa2e50e3b86710a40c412bacac34725451b9aace08da3ec691b": {"address": "0xb4e5b19D5fdE5ce3D44fbCa25407DA59f3361Cb7", "txHash": "0xb65b0ccd3ab951e7aa1107d03e832234e6de04f4eae664a4af93428d4f998ca8", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "b3675980246a6e48f2ba4af2d0cd49fb28bbef817c5cd8d40b598ef166156064": {"address": "0x445f114E0F7131FE0E62768849272B6005d68612", "txHash": "0xbbf1707abeee2cfb8daee08e9aa9e901e2ea95c72f3045e2a8320bdcb9b4d0f9", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ef7ebbda6f40a9bec2e5c0cb91bccbb9ce7543e2128334624429387a78199411": {"address": "0x25B4480F45dbAE9ab672755DdC6365449fF4d512", "txHash": "0xde209d53f67313b2ed46112e7a0aa36e21c7447d2ddd343d5a6ad5b66ddf0b15", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "fb9d14467ef83043b9a7243a2a7730b8fa887800e7dc84e19e8635b6dae0d096": {"address": "0x16E64F680409E2d4B480449Fce16AdeB54F52AC8", "txHash": "0x8bd614345447d51337bd8674bfdeb92a8435f8dc7b0b64dcf553e854871275d9", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "e3de976e7d384212e0a82586f28570da968647ffdb80d47d754101812f5e6694": {"address": "0xDC2Ae75ED84EbBd84b7228C18428086fc55EFE2A", "txHash": "0x0238c0f71ff95a350e9b39b31aa1fec1c79575bc03d9e8df3ca43bc6b8bddb69", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "fe4e7904f4d708ccad6bab29dcbb619a24571bf594bb148ae1c4711d30b6209c": {"address": "0xcAD997Eee35b09F9D7AEeE1939ae6fd0621989Dd", "txHash": "0x7a3d33fe256b67ad4b91574d6366547c8088db13a013d2be85d0d9a796038370", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "1457ebf050ba1fcce160eac354bdda4e46f60dc66b98383f616174b95e3995e2": {"address": "0x8EFbeEEB274F9397760836bE379E8e695D3C27Cd", "txHash": "0xe09a800159809fc4aef83799d0072d19a45ca2c563a994ea18c58f8a27c34b95", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "b945c550735e3c47dd05372675b449b1dc91e0d3635dc3b2e922a296dbec3cb0": {"address": "0x125B722Ecdd34267574FCC02Ce797D7341e4F16f", "txHash": "0x5bd729ee42e5ff761285bdbf3a735ff0c4dfdaee5192b09108e4acfdc3a775b0", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "6e8db607b2036cff852f4ebcb52ab8d116fb4e48b4cb277fbe82645103f33df0": {"address": "0x5F1FF56A7b91ABc411B72e4a6b24D3FD7Ddc9B5D", "txHash": "0xec51ad95f431a60818d0cf6ade6ca366bf8a0aaa8700d6b449a79c9013667ded", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "e89877ab1b67c8a3ed618b79620a257506a3c143490dc9278080f3e839ce46b2": {"address": "0x6a907016bdD09339E6758b6cac31e1b698b28832", "txHash": "0xd3f1ffa27d972b42363778f5db56b7b81ce46fcf89463b3c858dfdaf9016bb67", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "860d6681c53e1fddfe346b231cc5ad5194dd406e66ae4af35835cc07a4d6e209": {"address": "0x1E1132cB89E2833Ba4d8CbB4E9a71ab4f13A583f", "txHash": "0xd7d1fc14bf5fb3cda4c539ab8fb8cf7aba76819467a37fee132051f45c45c411", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "60b46dc0521ec5f2ff59a09a0fa4edb8ad1d4d6b4d734d17bae5ddb882a08342": {"address": "0xd3f9646c642c56859219532321f162EDF7414d75", "txHash": "0x8210ba270b4357a790f34516f15f6b66c873b9e8a3bf8866f60c758ef705fb64", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "653d561b54dcf0b12b6837980c66030561271a1bfe7520cca61a9ca62e67a858": {"address": "0xd26FB878589BCca77d38b79019758B24b8F715b9", "txHash": "0x0a876799b8be35a3e6427dc48da652f9c6ec082347efdce52c737d6f9e67e0d1", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "ae99a3c4e43db55343d979e12607207b94c4ffef0847e9752c21f7b151c46694": {"address": "0x7fc6Ae0287365D1Baf985Deb9A1851606df7F999", "txHash": "0x1aca624650c1e4f5ceb9139fc5ee2cc781e396460484d45d377832c829530c04", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}, {"label": "isRequestMinted", "offset": 0, "slot": "3", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:19"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}, "0923dd925802ba369e94d9018032c88474c1a392a8d53efdc08ba0d1775a7726": {"address": "0x66BcdA0a11EF983985F0B7bf6cC669BE52665E60", "txHash": "0xe0c44392bf2b5a23e48a8c98f388064fcb1f383a87fa7fb9ce964bab8ac67e7b", "layout": {"solcVersion": "0.8.24", "storage": [{"label": "_signer", "offset": 0, "slot": "0", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:15"}, {"label": "sbt", "offset": 0, "slot": "1", "type": "t_address", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:16"}, {"label": "isMinted", "offset": 0, "slot": "2", "type": "t_mapping(t_string_memory_ptr,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:18"}, {"label": "isRequestMinted", "offset": 0, "slot": "3", "type": "t_mapping(t_bytes32,t_bool)", "contract": "SBTMinting", "src": "contracts/SBTMinting.sol:19"}], "types": {"t_address": {"label": "address", "numberOfBytes": "20"}, "t_bool": {"label": "bool", "numberOfBytes": "1"}, "t_struct(InitializableStorage)65_storage": {"label": "struct Initializable.InitializableStorage", "members": [{"label": "_initialized", "type": "t_uint64", "offset": 0, "slot": "0"}, {"label": "_initializing", "type": "t_bool", "offset": 8, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(OwnableStorage)14_storage": {"label": "struct OwnableUpgradeable.OwnableStorage", "members": [{"label": "_owner", "type": "t_address", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_struct(ReentrancyGuardStorage)195_storage": {"label": "struct ReentrancyGuardUpgradeable.ReentrancyGuardStorage", "members": [{"label": "_status", "type": "t_uint256", "offset": 0, "slot": "0"}], "numberOfBytes": "32"}, "t_uint256": {"label": "uint256", "numberOfBytes": "32"}, "t_uint64": {"label": "uint64", "numberOfBytes": "8"}, "t_bytes32": {"label": "bytes32", "numberOfBytes": "32"}, "t_mapping(t_bytes32,t_bool)": {"label": "mapping(bytes32 => bool)", "numberOfBytes": "32"}, "t_mapping(t_string_memory_ptr,t_bool)": {"label": "mapping(string => bool)", "numberOfBytes": "32"}, "t_string_memory_ptr": {"label": "string", "numberOfBytes": "32"}, "t_string_storage": {"label": "string"}}, "namespaces": {"erc7201:openzeppelin.storage.ReentrancyGuard": [{"contract": "ReentrancyGuardUpgradeable", "label": "_status", "type": "t_uint256", "src": "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol:40", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Ownable": [{"contract": "OwnableUpgradeable", "label": "_owner", "type": "t_address", "src": "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol:24", "offset": 0, "slot": "0"}], "erc7201:openzeppelin.storage.Initializable": [{"contract": "Initializable", "label": "_initialized", "type": "t_uint64", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:69", "offset": 0, "slot": "0"}, {"contract": "Initializable", "label": "_initializing", "type": "t_bool", "src": "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol:73", "offset": 8, "slot": "0"}]}}}}}