// SPDX-License-Identifier: MIT
// Compatible with OpenZeppelin Contracts ^5.0.0
pragma solidity ^0.8.20;

import "./interfaces/ISBT.sol";
import "@openzeppelin/contracts/token/ERC721/IERC721.sol";
import "@openzeppelin/contracts-upgradeable/access/OwnableUpgradeable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/Initializable.sol";
import "@openzeppelin/contracts-upgradeable/proxy/utils/UUPSUpgradeable.sol";
import "@openzeppelin/contracts/utils/cryptography/ECDSA.sol";
import "@openzeppelin/contracts/utils/cryptography/MessageHashUtils.sol";
import "@openzeppelin/contracts-upgradeable/utils/ReentrancyGuardUpgradeable.sol";

contract SBTMinting is Initializable, OwnableUpgradeable, UUPSUpgradeable, ReentrancyGuardUpgradeable {
    address private _signer;
    address public sbt;

    mapping (string => bool) public isMinted;
    mapping (bytes32 => bool) public isRequestMinted;

    struct MintRequest {
        address buyer;
        uint256 price;
        uint256 deadline;
        bytes32 requestId;
        string  did;
    }

    error InvalidSignature();
    error Expired();
    error AlreadyMinted();
    error InvalidPaymentAmount();

    event MintSBT(bytes32 requestId, uint256 tokenId);

    /// @custom:oz-upgrades-unsafe-allow constructor
    constructor() {
        _disableInitializers();
    }

    function initialize(address initialOwner) initializer public {
        __Ownable_init(initialOwner);
        __UUPSUpgradeable_init();
    }

    function _authorizeUpgrade(address newImplementation)
        internal
        onlyOwner
        override
    {}

    function setSBT(address _sbt) public onlyOwner {
        sbt = _sbt;
    }

    function setSigner(address signer) public onlyOwner {
        _signer = signer;
    }

    function getHash(MintRequest calldata request) public pure returns (bytes32) {
        return keccak256(abi.encodePacked(request.buyer, request.price, request.deadline, request.requestId, request.did));
    }

    function getSigner() public view returns (address) {
        return _signer;
    }

    function mintSBT(MintRequest calldata request, bytes calldata signature) public payable nonReentrant {
        if (request.deadline < block.timestamp) revert Expired();
        if (isMinted[request.did]) revert AlreadyMinted();
        if (msg.value != request.price) revert InvalidPaymentAmount();
        if (isRequestMinted[request.requestId]) revert AlreadyMinted();
        bytes32 messageHash = MessageHashUtils.toEthSignedMessageHash(getHash(request));
        address signer = ECDSA.recover(messageHash, signature);
        if (signer != _signer) revert InvalidSignature();
        isMinted[request.did] = true;
        isRequestMinted[request.requestId] = true;
        uint256 tokenId = ISBT(sbt).safeMint(request.buyer);

        emit MintSBT(request.requestId, tokenId);
    }

    function withdraw() public onlyOwner {
        payable(owner()).transfer(address(this).balance);
    }

    // prevent accidental sending of ether to the contract
    receive() external payable {
        revert();
    }
}
