// SPDX-License-Identifier: MIT
// Compatible with OpenZeppelin Contracts ^5.0.0
pragma solidity ^0.8.20;

import "@openzeppelin/contracts/token/ERC721/ERC721.sol";
import "@openzeppelin/contracts/access/Ownable.sol";

contract SBT is ERC721, Ownable {
    uint256 private _nextTokenId;
    string public baseURI;

    address public mintingContract;

    constructor()
        ERC721("SBT", "SBT")
        Ownable(_msgSender())
    {}

    function safeMint(address to) public returns (uint256 tokenId){
        require(_msgSender() == mintingContract || _msgSender() == owner(), "SBT: caller is not the minting contract or owner");
        tokenId = _nextTokenId++;
        _safeMint(to, tokenId);
    }

    // batch minting
    function safeMintBatch(address to, uint256 amount) public {
        require(_msgSender() == mintingContract || _msgSender() == owner(), "SBT: caller is not the minting contract or owner");
        for (uint256 i = 0; i < amount; i++) {
            uint256 tokenId = _nextTokenId++;
            _safeMint(to, tokenId);
        }
    }

    function setMintingContract(address _mintingContract) public onlyOwner {
        mintingContract = _mintingContract;
    }
    
    // override baseURI to support dynamic URI
    function _baseURI() internal view override returns (string memory) {
        return baseURI;
    }

    function setBaseURI(string memory uri) public onlyOwner {
        baseURI = uri;
    }

    // override prevent token transfer, since the NFT is soul bound
    function transferFrom(address from, address to, uint256 tokenId) public override(ERC721) {
        if (from != owner() && to != owner() && from != mintingContract && to != mintingContract )revert("SBT: token is soul bound");
        super.transferFrom(from, to, tokenId);
    }

}
